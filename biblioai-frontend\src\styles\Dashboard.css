/* Dashboard Styles with Dark Mode Support */

/* Base dashboard styles */
.dashboard-container {
  transition: all 0.3s ease;
}

/* Header styles */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.dashboard-header-title {
  flex-shrink: 0;
  min-width: 0;
}

.dashboard-header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

/* Ensure logout button is always visible */
.logout-button {
  order: 999;
  flex-shrink: 0 !important;
  min-width: auto !important;
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: stretch;
  }

  .dashboard-header-actions {
    justify-content: center;
    width: 100%;
  }

  .logout-button {
    order: 1;
    align-self: flex-end;
  }
}

/* Card styles */
.dashboard-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

/* Dashboard tabs */
.dashboard-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1rem;
  overflow-x: auto;
  scrollbar-width: thin;
}

.dashboard-tab {
  padding: 0.75rem 1rem;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.dashboard-tab.active {
  border-bottom-color: #3b82f6;
  color: #3b82f6;
}

.dashboard-tab:hover:not(.active) {
  border-bottom-color: #e5e7eb;
}

/* Dashboard panels */
.dashboard-panel {
  padding: 1.5rem;
  border-radius: 8px;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

/* Stats cards */
.stats-card {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stats-card-icon {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  margin-bottom: 0.75rem;
}

/* Activity list */
.activity-list {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.activity-item {
  padding: 1rem;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background-color: #f9fafb;
}

.activity-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: #f3f4f6;
  margin-right: 1rem;
}

/* Search box */
.search-box {
  display: flex;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.search-input {
  flex-grow: 1;
  border: none;
  outline: none;
  background: transparent;
}

/* AI Chat Assistant */
.chat-assistant-bubble {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 9999px;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.chat-assistant-bubble:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Notification panel */
.notification-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 24rem;
  max-width: 100%;
  background-color: #ffffff;
  box-shadow: -4px 0 6px rgba(0, 0, 0, 0.1);
  z-index: 50;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification-panel.open {
  transform: translateX(0);
}

.notification-header {
  padding: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e7eb;
}

.notification-item {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f9fafb;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

/* Loading spinner */
.loading-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-spinner {
  border: 4px solid rgba(229, 231, 235, 0.3);
  border-radius: 50%;
  border-top: 4px solid #3b82f6;
  width: 3rem;
  height: 3rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Buttons */
.dashboard-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
  text-decoration: none;
  white-space: nowrap;
  min-height: 2.5rem;
}

.dashboard-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.dashboard-btn-primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.dashboard-btn-primary:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

.dashboard-btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  border-color: #e5e7eb;
}

.dashboard-btn-secondary:hover {
  background-color: #e5e7eb;
  border-color: #d1d5db;
}

.dashboard-btn-danger {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.dashboard-btn-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

/* Responsive button adjustments */
@media (max-width: 640px) {
  .dashboard-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    min-height: 2.25rem;
  }

  .dashboard-btn .hidden-mobile {
    display: none;
  }
}

/* Empty states */
.empty-state {
  padding: 3rem 1.5rem;
  text-align: center;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #d1d5db;
}

/* DARK MODE STYLES */
html.dark .dashboard-card {
  background-color: #1e293b;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

html.dark .dashboard-tabs {
  border-bottom-color: #334155;
}

html.dark .dashboard-tab:hover:not(.active) {
  border-bottom-color: #475569;
}

html.dark .dashboard-tab.active {
  border-bottom-color: #60a5fa;
  color: #60a5fa;
}

html.dark .dashboard-panel {
  background-color: #1e293b;
}

html.dark .stats-card {
  background-color: #1e293b;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

html.dark .activity-item {
  border-bottom-color: #334155;
}

html.dark .activity-item:hover {
  background-color: #1e293b;
}

html.dark .activity-icon {
  background-color: #334155;
}

html.dark .search-box {
  background-color: #1e293b;
  border-color: #334155;
}

html.dark .search-input {
  color: #f1f5f9;
}

html.dark .search-box:focus-within {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.3);
}

html.dark .chat-assistant-bubble {
  background-color: #60a5fa;
}

html.dark .notification-panel {
  background-color: #1e293b;
  box-shadow: -4px 0 6px rgba(0, 0, 0, 0.3);
}

html.dark .notification-header {
  border-bottom-color: #334155;
}

html.dark .notification-item {
  border-bottom-color: #334155;
}

html.dark .notification-item:hover {
  background-color: #273548;
}

html.dark .notification-item.unread {
  background-color: #0f2942;
}

html.dark .loading-spinner {
  border-color: rgba(51, 65, 85, 0.3);
  border-top-color: #60a5fa;
}

html.dark .dashboard-btn-secondary {
  background-color: #334155;
  color: #e2e8f0;
}

html.dark .dashboard-btn-secondary:hover {
  background-color: #475569;
}

html.dark .empty-state-icon {
  color: #475569;
}

/* Enhanced dark mode UI elements */
html.dark .stats-card-value {
  color: #f1f5f9;
}

html.dark .stats-card-label {
  color: #cbd5e1;
}

html.dark .dashboard-container {
  background-color: #0f172a;
}

html.dark .dropdown-menu {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.4);
}

html.dark .dropdown-item {
  color: #e2e8f0;
}

html.dark .dropdown-item:hover {
  background-color: #273548;
}

/* Enhanced scrollbars for dark mode */
html.dark *::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

html.dark *::-webkit-scrollbar-track {
  background: #1e293b;
}

html.dark *::-webkit-scrollbar-thumb {
  background-color: #475569;
  border-radius: 20px;
}

html.dark *::-webkit-scrollbar-thumb:hover {
  background-color: #64748b;
}
