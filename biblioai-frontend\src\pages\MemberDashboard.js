import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
// import Header from '../components/layout/Header';
// import Footer from '../components/layout/Footer';
import ReservationList from '../components/member/ReservationList';
import BorrowingList from '../components/member/BorrowingList';
import NotificationPanel from '../components/member/NotificationPanel';
import BookSearch from '../components/member/BookSearch';
import AIRecommendations from '../components/member/AIRecommendations';
import AIChatAssistant from '../components/member/AIChatAssistant';
import ThemeLanguageSettings from '../components/settings/ThemeLanguageSettings';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { getDashboardSummary } from '../services/memberService';
import { getUserNotifications } from '../services/notificationService';
import { logout } from '../services/authService';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import '../styles/Dashboard.css';

const MemberDashboard = () => {
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [dashboardSummary, setDashboardSummary] = useState(null);
  const [notificationPanelOpen, setNotificationPanelOpen] = useState(false);
  const [unreadNotifications, setUnreadNotifications] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Navigation hook
  const navigate = useNavigate();

  // Theme and Language hooks
  const { isDarkMode, getCurrentColors, toggleTheme } = useTheme();
  const { t, isLoading: langLoading } = useLanguage();
  const colors = getCurrentColors();

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }

    // Fetch dashboard data
    fetchDashboardData();
    fetchNotificationCount();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const data = await getDashboardSummary();
      setDashboardSummary(data);
      setError('');
    } catch (err) {
      setError(err.message || 'Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const fetchNotificationCount = async () => {
    try {
      const data = await getUserNotifications(1, 1, true); // Get just the count
      setUnreadNotifications(data.unread_count || 0);
    } catch (err) {
      console.error('Failed to fetch notification count:', err);
    }
  };

  const handleLogout = async () => {
    try {
      // Show confirmation dialog
      const confirmLogout = window.confirm(t('auth.confirmLogout') || 'Are you sure you want to logout?');
      if (!confirmLogout) {
        return;
      }

      // Clear authentication data
      logout();

      // Redirect to login page
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if there's an error
      logout();
      navigate('/login', { replace: true });
    }
  };

  const tabs = [
    { id: 'overview', name: t('dashboard.overview'), icon: '📊' },
    { id: 'search', name: t('books.searchBooks'), icon: '🔍' },
    { id: 'ai-recommendations', name: t('ai.recommendations'), icon: '🤖' },
    { id: 'borrowings', name: t('borrowing.myBorrowings'), icon: '📚' },
    { id: 'reservations', name: t('reservations.myReservations'), icon: '📋' },
    { id: 'settings', name: t('settings.title'), icon: '⚙️' },
  ];

  // Show loading if language is still loading
  if (langLoading) {
    return <LoadingSpinner message="Loading language..." />;
  }

  return (
    <div
      className="min-h-screen overflow-auto transition-colors duration-200 dashboard-container"
      style={{ backgroundColor: colors.background }}
    >
      <main className="w-full h-full px-4 sm:px-6 lg:px-8 py-6">
        {/* Header with user info, notifications, theme toggle, and logout */}
        <div className="dashboard-header">
          <div className="dashboard-header-title">
            <h1
              className="text-2xl sm:text-3xl font-bold transition-colors duration-200"
              style={{ color: colors.text }}
            >
              {t('nav.dashboard')}
            </h1>
            <p
              className="mt-1 text-sm transition-colors duration-200"
              style={{ color: colors.textSecondary }}
            >
              {t('dashboard.welcome')}{user ? `, ${user.username}` : ''}! {t('dashboard.subtitle')}.
            </p>
          </div>

          {/* Action buttons container */}
          <div className="dashboard-header-actions">
            {/* Theme Toggle Button */}
            <button
              onClick={toggleTheme}
              className="dashboard-btn dashboard-btn-secondary"
              style={{
                backgroundColor: colors.surface,
                color: colors.text,
                borderColor: colors.border
              }}
              title={isDarkMode ? t('settings.lightMode') : t('settings.darkMode')}
            >
              <span className="mr-1 sm:mr-2">{isDarkMode ? '☀️' : '🌙'}</span>
              <span className="hidden sm:inline">{isDarkMode ? t('settings.lightMode') : t('settings.darkMode')}</span>
            </button>

            {/* Notifications Button */}
            <button
              onClick={() => setNotificationPanelOpen(true)}
              className="dashboard-btn dashboard-btn-secondary relative"
              style={{
                backgroundColor: colors.surface,
                color: colors.text,
                borderColor: colors.border
              }}
              title={t('notifications.title')}
            >
              <svg className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 17H4l5 5v-5zM12 3v18" />
              </svg>
              <span className="hidden sm:inline">{t('notifications.title')}</span>
              {unreadNotifications > 0 && (
                <span
                  className="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 rounded-full text-xs font-bold min-w-[1.25rem] h-5"
                  style={{ backgroundColor: colors.error, color: 'white' }}
                >
                  {unreadNotifications > 99 ? '99+' : unreadNotifications}
                </span>
              )}
            </button>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className="dashboard-btn dashboard-btn-danger logout-button"
              style={{
                backgroundColor: colors.error,
                color: 'white',
                borderColor: colors.error
              }}
              title={t('auth.logout')}
            >
              <svg className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              <span className="hidden sm:inline">{t('auth.logout')}</span>
              <span className="sm:hidden">🚪</span>
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="dashboard-tabs mb-6 transition-colors duration-200">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`dashboard-tab font-medium text-sm transition-colors duration-200 ${activeTab === tab.id ? 'active' : ''}`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'overview' && (
            <div>
              {loading ? (
                <div className="loading-spinner-container py-8">
                  <div className="loading-spinner"></div>
                </div>
              ) : error ? (
                <div className="border rounded-md p-4 transition-colors duration-200 alert-error">
                  <div className="flex">
                    <div className="ml-3">
                      <h3
                        className="text-sm font-medium transition-colors duration-200"
                        style={{ color: colors.error }}
                      >
                        {t('common.error')}
                      </h3>
                      <div className="mt-2 text-sm" style={{ color: colors.error }}>
                        <p>{error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                    <div className="stats-card overflow-hidden shadow rounded-lg transition-colors duration-200">
                      <div className="p-5">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <div
                              className="stats-card-icon rounded-md flex items-center justify-center"
                              style={{ backgroundColor: colors.primary }}
                            >
                              <span className="text-white text-sm">📚</span>
                            </div>
                          </div>
                          <div className="ml-5 w-0 flex-1">
                            <dl>
                              <dt
                                className="text-sm font-medium truncate transition-colors duration-200"
                                style={{ color: colors.textSecondary }}
                              >
                                {t('dashboard.totalBooks')}
                              </dt>
                              <dd
                                className="text-lg font-medium transition-colors duration-200"
                                style={{ color: colors.text }}
                              >
                                {dashboardSummary?.summary?.active_borrowings || 0}
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="stats-card overflow-hidden shadow rounded-lg transition-colors duration-200">
                      <div className="p-5">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <div
                              className="w-8 h-8 rounded-md flex items-center justify-center"
                              style={{ backgroundColor: colors.success }}
                            >
                              <span className="text-white text-sm">📋</span>
                            </div>
                          </div>
                          <div className="ml-5 w-0 flex-1">
                            <dl>
                              <dt
                                className="text-sm font-medium truncate transition-colors duration-200"
                                style={{ color: colors.textSecondary }}
                              >
                                {t('reservations.title')}
                              </dt>
                              <dd
                                className="text-lg font-medium transition-colors duration-200"
                                style={{ color: colors.text }}
                              >
                                {dashboardSummary?.summary?.active_reservations || 0}
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="stats-card overflow-hidden shadow rounded-lg transition-colors duration-200">
                      <div className="p-5">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <div
                              className="w-8 h-8 rounded-md flex items-center justify-center"
                              style={{ backgroundColor: colors.error }}
                            >
                              <span className="text-white text-sm">⚠️</span>
                            </div>
                          </div>
                          <div className="ml-5 w-0 flex-1">
                            <dl>
                              <dt
                                className="text-sm font-medium truncate transition-colors duration-200"
                                style={{ color: colors.textSecondary }}
                              >
                                {t('dashboard.overdueBooks')}
                              </dt>
                              <dd
                                className="text-lg font-medium transition-colors duration-200"
                                style={{ color: colors.text }}
                              >
                                {dashboardSummary?.summary?.overdue_borrowings || 0}
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="stats-card overflow-hidden shadow rounded-lg transition-colors duration-200">
                      <div className="p-5">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <div
                              className="w-8 h-8 rounded-md flex items-center justify-center"
                              style={{ backgroundColor: colors.warning }}
                            >
                              <span className="text-white text-sm">💰</span>
                            </div>
                          </div>
                          <div className="ml-5 w-0 flex-1">
                            <dl>
                              <dt
                                className="text-sm font-medium truncate transition-colors duration-200"
                                style={{ color: colors.textSecondary }}
                              >
                                {t('members.fines')}
                              </dt>
                              <dd
                                className="text-lg font-medium transition-colors duration-200"
                                style={{ color: colors.text }}
                              >
                                ${dashboardSummary?.summary?.total_fines?.toFixed(2) || '0.00'}
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Recent Activity */}
                  {dashboardSummary?.recent_history && dashboardSummary.recent_history.length > 0 && (
                    <div className="activity-list shadow overflow-hidden sm:rounded-md transition-colors duration-200">
                      <div className="px-4 py-5 sm:px-6">
                        <h3
                          className="text-lg leading-6 font-medium transition-colors duration-200"
                          style={{ color: colors.text }}
                        >
                          {t('dashboard.recentActivity')}
                        </h3>
                        <p
                          className="mt-1 max-w-2xl text-sm transition-colors duration-200"
                          style={{ color: colors.textSecondary }}
                        >
                          {t('dashboard.recentActivity')}
                        </p>
                      </div>
                      <ul className="divide-y transition-colors duration-200">
                        {dashboardSummary.recent_history.slice(0, 5).map((activity) => (
                          <li
                            key={activity.id}
                            className="activity-item px-4 py-4 sm:px-6 transition-colors duration-200"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  <div
                                    className="activity-icon h-10 w-10 rounded-full flex items-center justify-center transition-colors duration-200"
                                  >
                                    <span
                                      className="text-sm font-medium transition-colors duration-200"
                                      style={{ color: colors.text }}
                                    >
                                      {activity.action === 'borrowed' ? '📚' :
                                       activity.action === 'returned' ? '✅' :
                                       activity.action === 'reserved' ? '📋' :
                                       activity.action === 'renewed' ? '🔄' : '📄'}
                                    </span>
                                  </div>
                                </div>
                                <div className="ml-4">
                                  <div
                                    className="text-sm font-medium transition-colors duration-200"
                                    style={{ color: colors.text }}
                                  >
                                    {activity.action.charAt(0).toUpperCase() + activity.action.slice(1)} "{activity.document?.title || 'Unknown Document'}"
                                  </div>
                                  <div
                                    className="text-sm transition-colors duration-200"
                                    style={{ color: colors.textSecondary }}
                                  >
                                    {new Date(activity.action_date).toLocaleDateString()}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </>
              )}
            </div>
          )}

          {activeTab === 'search' && <BookSearch />}
          {activeTab === 'ai-recommendations' && <AIRecommendations />}
          {activeTab === 'borrowings' && <BorrowingList />}
          {activeTab === 'reservations' && <ReservationList />}
          {activeTab === 'settings' && <ThemeLanguageSettings />}
        </div>

        {/* Notification Panel */}
        <div className={`notification-panel ${notificationPanelOpen ? 'open' : ''}`}>
          <NotificationPanel
            isOpen={notificationPanelOpen}
            onClose={() => setNotificationPanelOpen(false)}
          />
        </div>

        {/* AI Chat Assistant - Floating */}
        <div className="chat-assistant-bubble">
          <AIChatAssistant />
        </div>
      </main>
    </div>
  );
};

export default MemberDashboard;
